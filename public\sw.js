if(!self.define){let e,n={};const s=(s,i)=>(s=new URL(s+".js",i).href,n[s]||new Promise(n=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=n,document.head.appendChild(e)}else e=s,importScripts(s),n()}).then(()=>{let e=n[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e}));self.define=(i,t)=>{const a=e||("document"in self?document.currentScript.src:"")||location.href;if(n[a])return;let c={};const r=e=>s(e,a),u={module:{uri:a},exports:c,require:r};n[a]=Promise.all(i.map(e=>u[e]||r(e))).then(e=>(t(...e),c))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"2a71c79571cb05c0eb0436004529e5f7"},{url:"/_next/static/chunks/1064-99ced236495f1d32.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1342-db6f506c5419ded7.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1348-c49a54fece5e8558.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1367.bb2df06dc667d61d.js",revision:"bb2df06dc667d61d"},{url:"/_next/static/chunks/1413-c159b17a98084fee.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1481-274f4a1f10ddfdbe.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1684-2e8a14b0e33c9ab9.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1861-fd2fe0e8fd35fc22.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/1921.86a9d9abb90b313a.js",revision:"86a9d9abb90b313a"},{url:"/_next/static/chunks/2170a4aa.01b7318bdd55379d.js",revision:"01b7318bdd55379d"},{url:"/_next/static/chunks/2848-f77454f6806a64e9.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/2960-17841c59874f2bbe.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/3085-790209181f6fb512.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/3127-5ae1e84114de87c5.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/3214-beb8b8e18da0537a.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/3617-59ff8789197f9f38.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/3664-b67f2fd0d14f9584.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/4052-80368a87abca4cd3.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/4684.e742382fff3366c3.js",revision:"e742382fff3366c3"},{url:"/_next/static/chunks/472.2c08b965bd9148e2.js",revision:"2c08b965bd9148e2"},{url:"/_next/static/chunks/4920-09438c9972cd7127.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/4982-dbc8e50c3d10c00b.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/4bd1b696-433475f3472104df.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/5152-351c313e21b729c7.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/5304.2a273b66a53cf873.js",revision:"2a273b66a53cf873"},{url:"/_next/static/chunks/5508.4b534adbdb889dff.js",revision:"4b534adbdb889dff"},{url:"/_next/static/chunks/5973-6abd49b6b36837d4.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/6060-bbb4d4805a904e8e.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/6534.373afdd255896fc6.js",revision:"373afdd255896fc6"},{url:"/_next/static/chunks/6874-240e4016d857918e.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/6967-f43c3d8629f3196e.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/699-4c48fe085addcd53.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/7655-369723c1a470d0dc.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/769.3da2bcf8764ae552.js",revision:"3da2bcf8764ae552"},{url:"/_next/static/chunks/8250-e5bb44473aeae522.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/8436.cab94b59cca0a8ff.js",revision:"cab94b59cca0a8ff"},{url:"/_next/static/chunks/870.2fe92867fd1b6f33.js",revision:"2fe92867fd1b6f33"},{url:"/_next/static/chunks/8894.f8d18a8e961d5c62.js",revision:"f8d18a8e961d5c62"},{url:"/_next/static/chunks/9190-563d403ce5c107dc.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/9341.a5e04b1003bfe050.js",revision:"a5e04b1003bfe050"},{url:"/_next/static/chunks/9413-10bcd622ad766098.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/9445-f4bac30505c757bf.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/9585.e8a13b33bd3ad93e.js",revision:"e8a13b33bd3ad93e"},{url:"/_next/static/chunks/9681.19d4e10e7ef3a40c.js",revision:"19d4e10e7ef3a40c"},{url:"/_next/static/chunks/9748-88df0698c2137452.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/dashboard/page-52fd6ead3aeeb66d.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/equipment/page-09d6691cfcca95de.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/layout-8bbc7a0ff12eb2a2.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/maintenance/page-2a4229ef5ea6b05f.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/qr-scanner/page-1efa5d52951babfa.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/repair-requests/page-d44ba5dfe1651478.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/reports/page-3bf2d1bde57cdfba.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/transfers/page-0c27e839fefddb0d.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/(app)/users/page-37799d0a23210883.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/_not-found/page-96a7a8162a2b0e3b.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/layout-d67527e5c79e6e3f.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/not-found-2b13751c226ac435.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/page-0951e14a706e6891.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/app/test-auth/page-ed491fa1ed5d7459.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/d0f5a89a.68ddfd0fe63b98b9.js",revision:"68ddfd0fe63b98b9"},{url:"/_next/static/chunks/framework-2c2be674e67eda3d.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/main-app-ada3b357e3214a45.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/main-f0c49f4ebb047f5e.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/pages/_app-a61587d9d4172ff4.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/pages/_error-85dc5b0cc18f4481.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-45e9ee074b7ab960.js",revision:"nikMwXQm9W1D8nBBg0z0r"},{url:"/_next/static/css/6d6500a6c2028797.css",revision:"6d6500a6c2028797"},{url:"/_next/static/nikMwXQm9W1D8nBBg0z0r/_buildManifest.js",revision:"133311efefbbfcabbc8c80b50a1344a4"},{url:"/_next/static/nikMwXQm9W1D8nBBg0z0r/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/firebase-messaging-sw.js",revision:"5608085f198815c08e341105c9a83617"},{url:"/icons/icon-192x192.png",revision:"4139946c7843179db799270dbb153298"},{url:"/icons/icon-512x512.png",revision:"1d782a98e4fa3291375722ab8c34ae7e"},{url:"/icons/icon-maskable-192x192.png",revision:"f39e1d51e59e07fc9c08100928903e55"},{url:"/icons/icon-maskable-512x512.png",revision:"2f25391928588879d93e452ca6ab0563"},{url:"/manifest.json",revision:"bf83d1aa4f044fc4308ef2b84977c6b7"},{url:"/screenshots/placeholder-desktop.png",revision:"68e709f9a056166bfd332c30135eb9c5"},{url:"/screenshots/placeholder-mobile.png",revision:"eef1e61d9677052619769ec64f38f643"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:n,event:s,state:i})=>n&&"opaqueredirect"===n.type?new Response(n.body,{status:200,statusText:"OK",headers:n.headers}):n}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const n=e.pathname;return!n.startsWith("/api/auth/")&&!!n.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
